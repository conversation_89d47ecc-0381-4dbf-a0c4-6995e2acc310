<!doctype html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no" />
    <title>Walldash</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&amp;family=Roboto+Flex:opsz,wght@8..144,100..1000&amp;display=swap" rel="stylesheet" />
    <style>
      .loader {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .loader {
        width: 60px;
        aspect-ratio: 1;
        display: flex;
      }
      .loader:before,
      .loader:after {
        content: '';
        flex: 1;
        background: #000000;
        animation: l21 2s infinite;
        border-radius: 100px 0 0 100px;
        transform-origin: top right;
        transform: translateY(calc(var(--s, 1) * 0%)) rotate(0);
      }
      .loader:after {
        transform-origin: bottom left;
        border-radius: 0 100px 100px 0;
        --s: -1;
      }
      @keyframes l21 {
        33% {
          transform: translate(0, calc(var(--s, 1) * 50%)) rotate(0);
        }
        66% {
          transform: translate(0, calc(var(--s, 1) * 50%)) rotate(-90deg);
        }
        90%,
        100% {
          transform: translate(calc(var(--s, 1) * -100%), calc(var(--s, 1) * 50%)) rotate(-90deg);
        }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loader"></div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
