import { ref } from 'vue'

import { api } from '@/api'
import type { DashUserinfoGet200Response } from '@/api-client'

const userinfo = ref<DashUserinfoGet200Response>()

export async function fetchUserInfo() {
  if (!userinfo.value) {
    const res = await api.dashUserinfoGet()
    userinfo.value = res.data
  }
  return userinfo.value
}

export async function hasPermission(operationId: string): Promise<boolean> {
  const info = await fetchUserInfo()
  return info.is_superuser || info.permissions!.includes(operationId)
}
