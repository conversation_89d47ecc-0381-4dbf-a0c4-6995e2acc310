import { updatePreset, usePreset as setPreset, definePreset } from '@primeuix/themes'
import type { AuraBaseDesignTokens } from '@primeuix/themes/aura/base'
import type { Preset } from '@primeuix/themes/types'
import { Mutex } from 'async-mutex'
import { computed, ref, watch } from 'vue'

function getPresetExt(color: PrimaryColor) {
  const components = {
    dataview: {
      content: {
        background: 'transparent',
      },
    },
    paginator: {
      root: {
        background: 'transparent',
      },
    },
  }

  if (color === 'noir') {
    return {
      components,
      semantic: {
        primary: {
          50: '{surface.50}',
          100: '{surface.100}',
          200: '{surface.200}',
          300: '{surface.300}',
          400: '{surface.400}',
          500: '{surface.500}',
          600: '{surface.600}',
          700: '{surface.700}',
          800: '{surface.800}',
          900: '{surface.900}',
          950: '{surface.950}',
        },
        colorScheme: {
          light: {
            primary: {
              color: '{primary.950}',
              contrastColor: '#ffffff',
              hoverColor: '{primary.800}',
              activeColor: '{primary.700}',
            },
            surface: {
              0: '#ffffff',
              50: '{zinc.50}',
              100: '{zinc.100}',
              200: '{zinc.200}',
              300: '{zinc.300}',
              400: '{zinc.400}',
              500: '{zinc.500}',
              600: '{zinc.600}',
              700: '{zinc.700}',
              800: '{zinc.800}',
              900: '{zinc.900}',
              950: '{zinc.950}',
            },
            highlight: {
              background: '{primary.950}',
              focusBackground: '{primary.700}',
              color: '#ffffff',
              focusColor: '#ffffff',
            },
          },
          dark: {
            primary: {
              color: '{primary.50}',
              contrastColor: '{primary.950}',
              hoverColor: '{primary.200}',
              activeColor: '{primary.300}',
            },
            surface: {
              0: '#ffffff',
              50: '{zinc.50}',
              100: '{zinc.100}',
              200: '{zinc.200}',
              300: '{zinc.300}',
              400: '{zinc.400}',
              500: '{zinc.500}',
              600: '{zinc.600}',
              700: '{zinc.700}',
              800: '{zinc.800}',
              900: '{zinc.900}',
              950: '{zinc.950}',
            },
            highlight: {
              background: '{primary.50}',
              focusBackground: '{primary.300}',
              color: '{primary.950}',
              focusColor: '{primary.950}',
            },
          },
        },
      },
    }
  } else {
    return {
      components,
      semantic: {
        primary: {
          50: `{${color}.50}`,
          100: `{${color}.100}`,
          200: `{${color}.200}`,
          300: `{${color}.300}`,
          400: `{${color}.400}`,
          500: `{${color}.500}`,
          600: `{${color}.600}`,
          700: `{${color}.700}`,
          800: `{${color}.800}`,
          900: `{${color}.900}`,
          950: `{${color}.950}`,
        },
        colorScheme: {
          light: {
            primary: {
              color: '{primary.500}',
              contrastColor: '#ffffff',
              hoverColor: '{primary.600}',
              activeColor: '{primary.700}',
            },
            text: {
              color: '{surface.700}',
            },
            content: {
              border: {
                color: '{surface.200}',
              },
            },
            surface: {
              0: 'color-mix(in srgb, var(--p-primary-950), white 100%)',
              50: 'color-mix(in srgb, var(--p-primary-950), white 95%)',
              100: 'color-mix(in srgb, var(--p-primary-950), white 90%)',
              200: 'color-mix(in srgb, var(--p-primary-950), white 80%)',
              300: 'color-mix(in srgb, var(--p-primary-950), white 70%)',
              400: 'color-mix(in srgb, var(--p-primary-950), white 60%)',
              500: 'color-mix(in srgb, var(--p-primary-950), white 50%)',
              600: 'color-mix(in srgb, var(--p-primary-950), white 40%)',
              700: 'color-mix(in srgb, var(--p-primary-950), white 30%)',
              800: 'color-mix(in srgb, var(--p-primary-950), white 20%)',
              900: 'color-mix(in srgb, var(--p-primary-950), white 10%)',
              950: 'color-mix(in srgb, var(--p-primary-950), white 5%)',
            },
            highlight: {
              background: '{primary.50}',
              focusBackground: '{primary.100}',
              color: '{primary.700}',
              focusColor: '{primary.800}',
            },
          },
          dark: {
            primary: {
              color: '{primary.400}',
              contrastColor: '{surface.900}',
              hoverColor: '{primary.300}',
              activeColor: '{primary.200}',
            },
            surface: {
              0: 'color-mix(in srgb, var(--surface-ground), white 100%)',
              50: 'color-mix(in srgb, var(--surface-ground), white 95%)',
              100: 'color-mix(in srgb, var(--surface-ground), white 90%)',
              200: 'color-mix(in srgb, var(--surface-ground), white 80%)',
              300: 'color-mix(in srgb, var(--surface-ground), white 70%)',
              400: 'color-mix(in srgb, var(--surface-ground), white 60%)',
              500: 'color-mix(in srgb, var(--surface-ground), white 50%)',
              600: 'color-mix(in srgb, var(--surface-ground), white 40%)',
              700: 'color-mix(in srgb, var(--surface-ground), white 30%)',
              800: 'color-mix(in srgb, var(--surface-ground), white 20%)',
              900: 'color-mix(in srgb, var(--surface-ground), white 10%)',
              950: 'color-mix(in srgb, var(--surface-ground), white 5%)',
            },
            highlight: {
              background: 'color-mix(in srgb, {primary.400}, transparent 84%)',
              focusBackground: 'color-mix(in srgb, {primary.400}, transparent 76%)',
              color: 'rgba(255,255,255,.87)',
              focusColor: 'rgba(255,255,255,.87)',
            },
          },
        },
      },
    }
  }
}

type ThemeMode = 'light' | 'dark' | 'system'

export const primaryColors = ['noir', 'emerald', 'green', 'lime', 'red', 'orange', 'amber', 'yellow', 'teal', 'cyan', 'sky', 'blue', 'indigo', 'violet', 'purple', 'fuchsia', 'pink', 'rose'] as const
type PrimaryColor = (typeof primaryColors)[number]

const surfaceBackgrounds = {
  light: {
    noir: 'linear-gradient(180deg, #F4F4F5 0%, rgba(212, 212, 216, 0.12) 100%)',
    blue: 'linear-gradient(180deg, #e0e7f5 0%, rgba(170, 194, 239, 0.06) 111.26%)',
    green: 'linear-gradient(180deg, #e0f5e1 0%, rgba(170, 239, 172, 0.06) 111.26%)',
    violet: 'linear-gradient(180deg, #e9e0f5 0%, rgba(198, 170, 239, 0.06) 111.26%)',
    orange: 'linear-gradient(180deg, #f5e9e0 0%, rgba(239, 199, 170, 0.06) 111.26%)',
    rose: 'linear-gradient(180deg, #f5e0e3 0%, rgba(239, 170, 180, 0.06) 111.26%)',
    cyan: 'linear-gradient(180deg, #e0f2f5 0%, rgba(170, 229, 239, 0.06) 111.26%)',
    pink: 'linear-gradient(180deg, #f5e0eb 0%, rgba(239, 170, 205, 0.06) 111.26%)',
    red: 'linear-gradient(180deg, #f5e0e0 0%, rgba(239, 170, 170, 0.06) 111.26%)',
    amber: 'linear-gradient(180deg, #f5ede0 0%, rgba(239, 214, 170, 0.06) 111.26%)',
    yellow: 'linear-gradient(180deg, #f5f0e0 0%, rgba(239, 222, 170, 0.06) 111.26%)',
    lime: 'linear-gradient(180deg, #edf5e0 0%, rgba(212, 239, 170, 0.06) 111.26%)',
    emerald: 'linear-gradient(180deg, #e0f5ee 0%, rgba(170, 239, 216, 0.06) 111.26%)',
    teal: 'linear-gradient(180deg, #e0f5f3 0%, rgba(170, 239, 231, 0.06) 111.26%)',
    sky: 'linear-gradient(180deg, #e0eef5 0%, rgba(170, 217, 239, 0.06) 111.26%)',
    purple: 'linear-gradient(180deg, #ebe0f5 0%, rgba(206, 170, 239, 0.06) 111.26%)',
    fuchsia: 'linear-gradient(180deg, #f2e0f5 0%, rgba(230, 170, 239, 0.06) 111.26%)',
    indigo: 'linear-gradient(180deg, #e0e1f5 0%, rgba(170, 171, 239, 0.06) 111.26%)',
  },
  dark: {
    noir: '#09090b',
    blue: '#000C23',
    green: '#00231B',
    violet: '#0E0023',
    orange: '#231500',
    rose: '#230023',
    cyan: '#001E23',
    pink: '#230012',
    red: '#230000',
    amber: '#231600',
    yellow: '#231B00',
    lime: '#152300',
    emerald: '#002318',
    teal: '#00231F',
    sky: '#001823',
    purple: '#120023',
    fuchsia: '#1F0023',
    indigo: '#000123',
  },
} satisfies {
  dark: Record<PrimaryColor, string>
  light: Record<PrimaryColor, string>
}

export const presets = {
  Aura: () => import('@primeuix/themes/aura').then((m) => m.default),
  Lara: () => import('@primeuix/themes/lara').then((m) => m.default),
  Nora: () => import('@primeuix/themes/nora').then((m) => m.default),
  Material: () => import('@primeuix/themes/material').then((m) => m.default),
}

/**
 * 加载 preset
 */
const loadPreset = (() => {
  const cache: Record<string, Preset> = {}
  const mutex = new Mutex()

  return (name: keyof typeof presets) => {
    return mutex.runExclusive(async () => {
      if (name in cache) {
        return cache[name]
      } else {
        return presets[name]().then((preset) => {
          cache[name] = preset
          return preset
        })
      }
    })
  }
})()

export async function getPreset() {
  const preset = await loadPreset(presetName.value)
  return definePreset(preset, getPresetExt(primaryColor.value))
}

const presetStorageKey = 'preset'
const primaryColorStorageKey = 'primaryColor'
const themeStorageKey = 'themeMode'

function getStoragePresetName() {
  const stored = localStorage.getItem(presetStorageKey) as keyof typeof presets
  if (stored in presets) {
    return stored
  } else {
    return 'Aura'
  }
}
function getStorageThemeMode(): ThemeMode {
  return (localStorage.getItem(themeStorageKey) as ThemeMode) || 'system'
}
function getStoargePrimaryColor(): PrimaryColor {
  return (localStorage.getItem(primaryColorStorageKey) as PrimaryColor | null) || 'noir'
}

export const primaryColor = ref(getStoargePrimaryColor())
export const themeMode = ref(getStorageThemeMode())

export const presetName = ref(getStoragePresetName())

/**
 * 当前 preset
 */
export const preset = ref<Preset>()
watch(
  presetName,
  async (value) => {
    preset.value = await loadPreset(value)
  },
  { immediate: true },
)

/**
 * 当前 preset 的原生颜色
 */
export const presetPrimitive = computed(() => {
  return preset.value?.primitive as Required<AuraBaseDesignTokens['primitive']>
})

/**
 * 当前主色
 */
export const presetPrimary = computed(() => {
  if (primaryColor.value === 'noir') return
  return presetPrimitive.value?.[primaryColor.value]
})

const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
const prefersColorSchemeDark = ref(mediaQuery.matches)
mediaQuery.addEventListener('change', function (event) {
  prefersColorSchemeDark.value = event.matches
})

export const isDark = computed(() => {
  if (themeMode.value === 'system') {
    return prefersColorSchemeDark.value
  } else {
    return themeMode.value === 'dark'
  }
})

watch(primaryColor, () => {
  localStorage.setItem(primaryColorStorageKey, primaryColor.value)
  updatePreset(getPresetExt(primaryColor.value))
})
watch(themeMode, () => {
  localStorage.setItem(themeStorageKey, themeMode.value)
})
watch(
  isDark,
  () => {
    const html = document.documentElement
    // 更新 HTML class
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  },
  { immediate: true },
)
watch(presetName, async () => {
  localStorage.setItem(presetStorageKey, presetName.value)
  setPreset(await getPreset())
})
watch(
  [isDark, primaryColor],
  () => {
    document.documentElement.style.setProperty('--surface-ground', isDark.value ? surfaceBackgrounds.dark[primaryColor.value] : surfaceBackgrounds.light[primaryColor.value])
  },
  { immediate: true },
)
