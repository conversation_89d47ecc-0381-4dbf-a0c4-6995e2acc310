import { createRouter, createWebHistory, RouterView, type RouteRecordRaw } from 'vue-router'

import { api } from '@/api'
import DashboardPage from '@/pages/DashboardPage.vue'
import LoginPage from '@/pages/LoginPage.vue'
import { fetchUserInfo, hasPermission } from '@/store/user'
import OverviewView from '@/views/OverviewView.vue'

export const RouteName = {
  Root: Symbol(),
  Overview: Symbol(),
  Login: Symbol(),
  TopicWallpapers: Symbol(),
  TopicPublished: Symbol(),
}

export interface MenuRouteMeta {
  label?: string
  icon?: string
  /**
   * 是否在菜单中隐藏
   */
  hidden?: boolean
  /**
   * 验证是否有访问权限
   */
  access?: () => Promise<boolean>
}

type MyRouteRecordRaw = RouteRecordRaw & { meta?: MenuRouteMeta; children?: MyRouteRecordRaw[] }

export const menuRoutes: MyRouteRecordRaw[] = [
  {
    path: '/overview',
    name: RouteName.Overview,
    component: OverviewView,
    meta: {
      label: '概览',
      icon: 'pi pi-chart-bar',
    },
  },
  {
    path: '/wallpapers',
    component: RouterView,
    meta: {
      label: '壁纸',
    },
    children: [
      {
        path: 'search',
        component: () => import('@/views/WallpaperSearchView.vue'),
        meta: {
          label: '搜索壁纸',
          icon: 'pi pi-search',
        },
      },
      {
        path: '',
        component: () => import('@/views/WallpaperListView.vue'),
        meta: {
          label: '壁纸列表',
          icon: 'pi pi-image',
        },
      },
      {
        path: 'upload',
        component: () => import('@/views/UploadWallapersView.vue'),
        meta: {
          label: '上传壁纸',
          icon: 'pi pi-upload',
          access: () => {
            return hasPermission(api.postUploadWallpapers.name)
          },
        },
      },
    ],
  },
  {
    path: '/topics',
    component: RouterView,
    meta: {
      label: '专题',
    },
    children: [
      {
        path: '',
        component: () => import('@/views/TopicListView.vue'),
        meta: {
          label: '专题管理',
          icon: 'pi pi-th-large',
        },
      },
      {
        name: RouteName.TopicWallpapers,
        path: ':id/wallpapers',
        component: () => import('@/views/TopicWallpaperListView.vue'),
        meta: {
          label: '专题壁纸',
          hidden: true,
        },
      },
      {
        name: RouteName.TopicPublished,
        path: 'published',
        component: () => import('@/views/ClientTopicListView.vue'),
        meta: {
          label: '发布管理',
          icon: 'pi pi-send',
        },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: RouteName.Root,
      component: DashboardPage,
      redirect: { name: RouteName.Overview },
      children: menuRoutes,
      meta: {
        access: async () => {
          return fetchUserInfo()
            .then(() => true)
            .catch(() => false)
        },
      },
    },
    { path: '/login', name: RouteName.Login, component: LoginPage },
  ] satisfies MyRouteRecordRaw[],
})

export default router

router.beforeEach(async (to) => {
  for (const item of to.matched) {
    const meta = <MenuRouteMeta>item.meta
    if (meta.access && !(await meta.access())) {
      return false // TODO 导航到 403 页面
    }
  }
})
