@import 'tailwindcss';
@import 'tailwindcss-primeui';

:root {
  --p-card-border-radius: var(--radius-2xl);
  --p-card-background: rgba(255, 255, 255, 0.64);
  --p-card-body-padding: --spacing(4);
  --p-card-shadow: var(--shadow-xs);
  --p-card-color: var(--p-surface-950);
  --p-card-title-font-size: var(--text-lg);

  @variant dark {
    --p-card-background: rgba(255, 255, 255, 0.08);
    --p-card-color: var(--p-surface-0);
  }

  @variant lg {
    --p-card-border-radius: var(--radius-3xl);
    --p-card-body-padding: --spacing(6);
  }
}

@custom-variant dark (&:where(.dark, .dark *));

@layer base {
  html {
    font-size: 14px;
  }

  body {
    font-family: Figtree, sans-serif;
    background: var(--surface-ground);
    color: var(--p-text-color);
    line-height: 1.2;
  }
}

@utility my-h-screen {
  height: 100vh;
  height: 100dvh; /* IOS 浏览器 100vh 会溢出，这里优先使用 100dvh */
}

@layer components {
  .card {
    @apply rounded-2xl bg-[rgba(255,255,255,.64)] p-4 shadow-xs lg:rounded-3xl lg:p-6 dark:bg-[rgba(255,255,255,.08)];
  }
}
