<script lang="ts" setup>
import AppLayout from '@/layout/AppLayout.vue'
</script>

<template>
  <img alt="App Light" class="pointer-events-none fixed top-0 left-0 hidden h-auto w-[144px] mix-blend-color-dodge dark:block" src="/images/app-light-1.png" />
  <img alt="App Light" class="pointer-events-none fixed top-0 right-0 hidden h-auto w-[766px] mix-blend-color-dodge dark:block" src="/images/app-light-2.png" />
  <img alt="App Light" class="pointer-events-none fixed bottom-0 left-52 hidden h-auto w-[327px] mix-blend-color-dodge dark:block" src="/images/app-light-3.png" />
  <AppLayout />
</template>
