<script lang="ts" setup>
import type { ChartData, ChartOptions } from 'chart.js'
import Chart from 'primevue/chart'
import { computed, ref } from 'vue'

const props = defineProps<{
  data: Array<{
    label: string
    value: number
    color?: string
  }>
}>()
const total = computed(() => props.data.reduce((acc, item) => acc + item.value, 0))
const chartData = computed<ChartData<'doughnut'>>(() => {
  const data = props.data.map((item) => item.value)
  const backgroundColors = props.data.map((item) => item.color)

  return {
    datasets: [
      {
        data,
        backgroundColor: backgroundColors,
        borderRadius: 6,
        borderColor: 'transparent',
        offset: 15,
      },
    ],
  }
})

const tooltipState = ref({
  opacity: 0,
  x: 0,
  y: 0,
  value: '',
})

const chartOptions: ChartOptions<'doughnut'> = {
  responsive: false,
  aspectRatio: 1.5,
  rotation: -90,
  circumference: 180,
  cutout: '80%',
  clip: false,
  plugins: {
    legend: {
      display: false, // 不显示图例
    },
    tooltip: {
      enabled: false, // 不显示 tooltip
      external: ({ tooltip }) => {
        tooltipState.value = {
          opacity: tooltip.opacity,
          x: tooltip.caretX,
          y: tooltip.caretY,
          value: tooltip.dataPoints[0].formattedValue,
        }
      },
    },
  },
}
</script>

<template>
  <div>
    <div class="relative">
      <Chart :data="chartData" :height="200" :options="chartOptions" type="doughnut" :width="300" />
      <div class="absolute bottom-8 left-1/2 z-20 flex -translate-x-1/2 flex-col items-center justify-end transition-all">
        <h2 class="text-surface-500 text-center font-medium dark:text-white/64">总计</h2>
        <p class="text-surface-950 dark:text-surface-0 mt-1 text-center text-2xl font-medium">{{ total }}</p>
      </div>
      <div
        v-if="tooltipState"
        class="dark:bg-surface-950 bg-surface-0 border-surface pointer-events-none absolute overflow-hidden rounded-[8px] border opacity-100 shadow-[0px_16px_32px_-12px_rgba(88,92,95,0.10)] transition-opacity"
        :style="{ opacity: tooltipState.opacity, top: `${tooltipState.y}px`, left: `${tooltipState.x}px` }"
      >
        <div class="itens-center flex min-w-[3rem] justify-center gap-2 px-3 py-1.5">
          <span class="text-surface-950 dark:text-surface-0 text-right text-base font-semibold">
            {{ tooltipState.value }}
          </span>
        </div>
      </div>
    </div>
    <div class="bg-surface-0 shadow-v1 flex w-full items-center justify-between gap-8 rounded-lg p-3 dark:bg-white/10">
      <div v-for="(item, idx) in data" :key="idx" class="flex flex-1 items-center">
        <div class="h-4 w-1 rounded-full shadow-[0px_3px_1px_0px_rgba(0,0,0,0.00),0px_2px_1px_0px_rgba(0,0,0,0.01),0px_1px_1px_0px_rgba(0,0,0,0.02),0px_0px_1px_0px_rgba(0,0,0,0.03)]" :style="{ background: item.color }"></div>
        <div class="text-surface-950 dark:text-surface-0 mr-2 ml-3 text-sm text-nowrap">{{ item.label }}</div>
        <div class="font-medium text-nowrap" :style="{ color: item.color }">{{ ((item.value / total) * 100).toFixed(2) }} %</div>
      </div>
    </div>
  </div>
</template>
