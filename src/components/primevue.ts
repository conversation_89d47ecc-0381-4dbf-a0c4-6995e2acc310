import {
  Form as PrimeForm,
  FormField as PrimeF<PERSON>Field,
  type FormEmitsOptions,
  type FormFieldEmitsOptions,
  type FormFieldProps,
  type FormProps,
} from '@primevue/forms'
import { useConfirm as usePrimeConfirm } from 'primevue'
import type { ConfirmationOptions } from 'primevue/confirmationoptions'
import Message, { type MessageProps } from 'primevue/message'
import { h, type SetupContext } from 'vue'

export function Form(props: FormProps, { slots, attrs }: SetupContext<FormEmitsOptions>) {
  return h(
    PrimeForm,
    {
      ...attrs,
      class: ['flex flex-col gap-4', attrs.class],
      ...props,
    },
    slots,
  )
}

export function FormField(
  props: FormFieldProps,
  { slots, attrs }: SetupContext<FormFieldEmitsOptions>,
) {
  return h(
    PrimeFormField,
    {
      ...attrs,
      class: ['flex flex-col gap-2', attrs.class],
      ...props,
    },
    slots,
  )
}

export function FormFieldMessage(props: MessageProps, { attrs, slots }: SetupContext) {
  return h(
    Message,
    {
      ...attrs,
      ...props,
      severity: 'error',
      size: 'small',
      variant: 'simple',
    },
    slots,
  )
}

export function useConfirm() {
  const confirm = usePrimeConfirm()

  return {
    require(option: Pick<ConfirmationOptions, 'message' | 'accept'>) {
      confirm.require({
        header: '等待确认',
        icon: 'pi pi-exclamation-triangle',
        acceptProps: {
          label: '是',
        },
        rejectProps: {
          label: '否',
          severity: 'secondary',
          outlined: true,
        },
        ...option,
      })
    },
  }
}
