<script lang="ts" setup>
defineProps<{
  icon: string
  variant?: 'clip'
}>()
</script>

<template>
  <button
    :class="[
      'text-color relative flex size-10 cursor-pointer items-center justify-center text-center transition-colors outline-none select-none',
      variant === 'clip' ? 'clip' : 'rounded-full hover:bg-white hover:dark:bg-[var(--p-content-hover-background)]',
    ]"
  >
    <i :class="icon" style="font-size: 1.125rem"></i>
  </button>
</template>

<style>
.clip {
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    opacity: 1;
    border: 1px solid var(--p-primary-color);
    transition: 0.5s;
    animation: 3s linear 0s infinite normal none running clippath;
    border-radius: 4px;
  }
  &::after {
    animation: 3s linear -1.5s infinite normal none running clippath;
  }
}
@keyframes clippath {
  0%,
  100% {
    clip-path: inset(0px 0px 90%);
  }
  25% {
    clip-path: inset(0px 90% 0px 0px);
  }
  50% {
    clip-path: inset(90% 0px 0px);
  }
  75% {
    clip-path: inset(0px 0px 0px 90%);
  }
}
</style>
