<script lang="ts" setup>
import DataView, { type DataViewProps, type DataViewSlots } from 'primevue/dataview'

interface Props extends DataViewProps {
  loading?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  alwaysShowPaginator: undefined,
})
defineSlots<DataViewSlots>()
const emit = defineEmits<{
  page: [event: { page: number }]
}>()
</script>

<template>
  <DataView
    v-bind="props"
    :pt="{
      root: { class: 'flex flex-col h-full' },
      content: { class: ['overflow-y-scroll', 'grow'] },
      emptyMessage: { class: ['h-full'] },
    }"
    @page="emit('page', $event)"
  >
    <template #empty>
      <div v-if="loading" class="flex h-full items-center justify-center">
        <i class="pi pi-spin pi-spinner"></i>
      </div>
      <div v-else class="flex h-full items-center justify-center">没有数据</div>
    </template>
    <template #list="slotProps">
      <div v-if="loading" class="flex h-full items-center justify-center">
        <i class="pi pi-spin pi-spinner"></i>
      </div>
      <template v-else>
        <slot name="list" v-bind="slotProps"></slot>
      </template>
    </template>
    <template #paginatorend>
      <slot name="paginatorend"></slot>
    </template>
  </DataView>
</template>
