<script setup lang="ts">
import Image, { type ImageProps } from 'primevue/image'
import { ref } from 'vue'

const props =
  defineProps<Pick<ImageProps, 'imageClass' | 'imageStyle' | 'src' | 'preview' | 'pt'>>()

const loading = ref(true)
function onLoad() {
  loading.value = false
  error.value = false
}

const error = ref(false)
function onError() {
  loading.value = false
  error.value = true
}
</script>

<template>
  <Image class="inline-block" v-bind="props">
    <template #image>
      <div class="relative">
        <span
          v-if="loading || error"
          class="bg-surface-100 dark:bg-surface-800 text-surface-500 absolute inset-0 flex items-center justify-center"
        >
          <i v-if="loading" class="pi pi-spin pi-spinner"></i>
          <i v-else-if="error" class="pi pi-exclamation-triangle"></i>
        </span>
        <img
          :class="imageClass"
          loading="lazy"
          :src="src"
          :style="imageStyle"
          @error="onError"
          @load="onLoad"
        />
      </div>
    </template>
  </Image>
</template>
