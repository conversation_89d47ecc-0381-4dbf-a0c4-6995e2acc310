<script lang="ts" setup>
import DataTable, { type DataTableProps } from 'primevue/datatable'

interface Props extends DataTableProps {
  emptyText?: string
}

const { rows, totalRecords, emptyText = '没有数据', ...props } = defineProps<Props>()
const currentPage = defineModel<number>('currentPage', { default: 1 })
const emit = defineEmits(['update:currentPage'])
</script>

<template>
  <DataTable
    v-bind="props"
    :first="(currentPage - 1) * (rows ?? 0)"
    lazy
    paginator
    :rows="rows"
    :showHeaders="value?.length !== 0"
    tableStyle="min-width: 50rem"
    :totalRecords="totalRecords"
    @page="
      ({ page }) => {
        emit('update:currentPage', page + 1)
      }
    "
  >
    <slot />
    <template #empty>
      <div class="flex min-h-24 items-center justify-center">
        {{ props.value?.length === 0 ? emptyText : '加载中...' }}
      </div>
    </template>
    <template #expansion="slotProps">
      <slot name="expansion" v-bind="slotProps"></slot>
    </template>
  </DataTable>
</template>
