<script setup lang="ts">
import AppLogo from './AppLogo.vue'
import AppSidebar from './AppSidebar.vue'
import AppTopbar from './AppTopbar.vue'

const version = __BUILD_TIME__
</script>

<template>
  <div class="my-h-screen flex space-x-3">
    <AppSidebar />

    <div class="grow overflow-scroll">
      <div class="mx-auto flex h-full max-w-[1540px] flex-col *:shrink-0">
        <header class="border-surface border-b px-4 py-3 md:px-8 lg:border-none">
          <AppTopbar />
        </header>

        <main class="grow px-4 py-8 md:px-8">
          <RouterView></RouterView>
        </main>

        <footer class="px-4 pt-6 pb-3 sm:pb-6 md:px-8">
          <div class="flex items-center justify-between">
            <div class="flex items-end gap-2"><AppLogo class="size-6" /><span class="uppercase">Walldash</span></div>
            <div class="text-muted-color text-sm">版本: {{ version }}</div>
          </div>
        </footer>
      </div>
    </div>
  </div>
</template>
