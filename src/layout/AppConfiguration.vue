<script lang="ts" setup>
import { Drawer, SelectButton } from 'primevue'
import { ref } from 'vue'

import { configurationVisible } from '../store/menu'
import { presets, primaryColors, primaryColor, themeMode, presetName } from '../store/palette'

const themeOptions = ref([
  { label: '浅色模式', icon: 'pi pi-sun', value: 'light' },
  { label: '深色模式', icon: 'pi pi-moon', value: 'dark' },
  { label: '跟随系统', icon: 'pi pi-desktop', value: 'system' },
])

const presetOptions = ref(Object.keys(presets))
</script>

<template>
  <Drawer v-model:visible="configurationVisible" header="设置" :modal="false" position="right" :style="{ width: '23rem' }">
    <div class="space-y-2">
      <div>
        <span class="text-muted-color text-sm font-semibold">主色调</span>
        <div class="flex flex-wrap gap-2 py-2">
          <button
            v-for="color in primaryColors"
            :key="color"
            :class="['outline-primary size-5 cursor-pointer rounded-full outline-offset-2', { 'outline-2': primaryColor === color }]"
            :style="{
              backgroundColor: color === 'noir' ? 'light-dark(rgb(0, 0, 0), rgb(255, 255, 255))' : `var(--p-${color}-500)`,
            }"
            :title="color"
            @click="primaryColor = color"
          ></button>
        </div>
      </div>
      <div>
        <span class="text-muted-color text-sm font-semibold">风格</span>
        <div class="py-2">
          <SelectButton v-model="presetName" :allow-empty="false" :options="presetOptions"></SelectButton>
        </div>
      </div>
      <div>
        <span class="text-muted-color text-sm font-semibold">外观</span>
        <div class="py-2">
          <SelectButton v-model="themeMode" :allow-empty="false" option-label="label" option-value="value" :options="themeOptions">
            <template #option="slotProps">
              <div>
                <i :class="slotProps.option.icon" style="font-size: 1.25rem"></i>
                <span class="mt-1 block text-xs">{{ slotProps.option.label }}</span>
              </div>
            </template>
          </SelectButton>
        </div>
      </div>
    </div>
  </Drawer>
</template>
