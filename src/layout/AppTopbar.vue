<script lang="ts" setup>
import IconButton from '@/components/IconButton.vue'

import AppBreadcurmb from './AppBreadcurmb.vue'
import AppUser from './AppUser.vue'
import { staticMenuVisible, drawerMenuVisible } from '../store/menu'
import { configurationVisible } from '../store/menu'
</script>

<template>
  <div class="text-color">
    <div class="flex justify-between">
      <IconButton class="block lg:hidden" icon="pi pi-bars" @click="drawerMenuVisible = true"></IconButton>
      <IconButton class="hidden lg:block" :icon="`pi ${staticMenuVisible ? 'pi-chevron-left' : 'pi-chevron-right'}`" @click="staticMenuVisible = !staticMenuVisible"></IconButton>
      <span class="border-surface mx-4 hidden w-[1px] self-stretch border-l lg:block"></span>
      <AppBreadcurmb />
      <div class="flex gap-4 lg:ml-auto">
        <IconButton icon="pi pi-palette" variant="clip" @click="configurationVisible = true"></IconButton>
        <AppUser />
      </div>
    </div>
  </div>
</template>
