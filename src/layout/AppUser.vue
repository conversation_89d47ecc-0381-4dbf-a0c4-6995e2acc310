<script lang="ts" setup>
import { Menu } from 'primevue'
import { ref } from 'vue'

import { api } from '@/api'
import IconButton from '@/components/IconButton.vue'

const menu = ref()
const menuItems = ref([
  {
    label: '退出登录',
    icon: 'pi pi-sign-out',
    command() {
      api.dashAuthTokenRevokeGet({ noAuth: true }).then(() => {
        window.location.reload()
      })
    },
  },
])

function toggle(event: Event) {
  menu.value.toggle(event)
}
</script>

<template>
  <IconButton icon="pi pi-user" @click="toggle"></IconButton>
  <Menu ref="menu" class="min-w-40!" :model="menuItems" :popup="true"></Menu>
</template>
