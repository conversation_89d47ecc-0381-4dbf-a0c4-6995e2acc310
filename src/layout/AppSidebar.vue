<script lang="ts" setup>
import { Drawer } from 'primevue'

import AppMenu from './AppMenu.vue'
import { staticMenuVisible, drawerMenuVisible } from '../store/menu'
</script>

<template>
  <!-- static menu -->
  <Transition name="slide">
    <div v-show="staticMenuVisible" class="hidden lg:block">
      <AppMenu />
    </div>
  </Transition>

  <!-- drawer menu -->
  <Drawer
    v-model:visible="drawerMenuVisible"
    :pt="{
      root: {
        class: 'w-auto! lg:hidden! bg-transparent! border-none! shadow-none!',
      },
      mask: {
        class: 'lg:hidden!',
      },
    }"
  >
    <template #container>
      <div class="dark:bg-surface-950 h-full rounded-tr-3xl rounded-br-3xl bg-white shadow-[rgba(0,0,0,.15)_0px_0px_1rem]">
        <AppMenu />
      </div>
    </template>
  </Drawer>
</template>

<style>
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
  margin-left: -17rem;
}
</style>
