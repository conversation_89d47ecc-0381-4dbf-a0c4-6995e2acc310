<script lang="ts" setup>
import type { MenuItem } from 'primevue/menuitem'
import { ref, watch } from 'vue'
import { useRoute, type RouteParamsGeneric } from 'vue-router'

function resolvePath(template: string, params: RouteParamsGeneric): string {
  return template.replace(/:([a-zA-Z_]\w*)/g, (_, key) => {
    if (params[key] == null) {
      throw new Error(`Missing parameter: ${key}`)
    }
    return encodeURIComponent(String(params[key]))
  })
}

const route = useRoute()
const breadcumbItems = ref<{ label: string; route?: string }[]>([])
watch(
  () => route.path,
  () => {
    const items: { label: string; route?: string }[] = []
    route.matched.forEach((m) => {
      if (m.meta?.label) {
        const currentPath = resolvePath(m.path, route.params)
        items.push({
          label: (m.meta as MenuItem).label as string,
          route: currentPath === route.path ? undefined : currentPath,
        })
      }
    })
    breadcumbItems.value = items
  },
  { immediate: true },
)
</script>

<template>
  <ul class="flex items-center gap-4">
    <template v-for="(item, idx) in breadcumbItems" :key="item.label">
      <li class="text-surface-950 dark:text-surface-0 text-xl">
        {{ item.label }}
      </li>
      <li v-if="breadcumbItems.length > 1 && idx < breadcumbItems.length - 1">/</li>
    </template>
  </ul>
</template>
