<script lang="ts" setup>
import Menu from 'primevue/menu'
import type { MenuItem } from 'primevue/menuitem'
import { onMounted, ref } from 'vue'
import { useRoute, type RouteRecordRaw } from 'vue-router'

import { menuRoutes, type MenuRouteMeta } from '@/router'

import AppTitle from './AppTitle.vue'

async function resolveMenuItems(routes: RouteRecordRaw[], parentPath = '') {
  const items: MenuItem[] = []
  for (const route of routes) {
    const meta: MenuRouteMeta = route.meta || {}

    if (meta.hidden) {
      continue
    }
    if (meta.access && !(await meta.access())) {
      continue
    }

    const to = parentPath && route.path ? parentPath + '/' + route.path : parentPath || route.path
    const item: MenuItem = { ...meta, to: to }
    if (route.children) {
      item.items = await resolveMenuItems(route.children, to)
    }
    items.push(item)
  }
  return items
}
const items = ref<MenuItem[]>()
onMounted(() => {
  resolveMenuItems(menuRoutes).then((data) => {
    items.value = data
  })
})
const route = useRoute()
</script>

<template>
  <div>
    <Menu
      :model="items"
      :pt="{
        root: {
          class: 'w-[17rem]',
        },
        list: {
          class: 'p-4 gap-1 flex flex-col',
        },
        submenuLabel: {
          class: 'text-surface-700 text-xs font-medium pb-1 not-first:mt-4',
        },
      }"
      unstyled
    >
      <template #start>
        <div class="p-4">
          <AppTitle />
        </div>
      </template>
      <template #item="{ item, props }">
        <RouterLink
          v-if="item.to"
          v-slot="{ href, navigate }"
          :class="[
            'text-surface-700 inline-block w-full rounded-full px-[.65rem] py-2 transition-colors dark:text-[rgba(255,255,255,0.48)]', // default
            item.to !== route.path && 'hover:text-surface-950 hover:font-medium dark:hover:text-[rgba(255,255,255,.75)]', // no active
            item.to === route.path && 'text-surface-950 bg-white font-medium dark:bg-[rgba(255,255,255,0.12)] dark:text-white', // active
          ]"
          custom
          :to="item.to"
        >
          <a :href="href" v-bind="props.action" @click="navigate">
            <i :class="item.icon" />
            <span class="ml-2">{{ item.label }}</span>
          </a>
        </RouterLink>
      </template>
    </Menu>
  </div>
</template>
