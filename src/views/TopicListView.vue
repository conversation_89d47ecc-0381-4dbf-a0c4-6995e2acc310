<script setup lang="ts">
import { Button } from 'primevue'
import { ref, watch } from 'vue'

import { api } from '@/api'
import type { DashTopicsGet200Response, DashTopicsGet200ResponseResultsInner } from '@/api-client'
import DataView from '@/components/DataView.vue'
import ViewLayout from '@/layout/ViewLayout.vue'
import TopicListItemView from '@/views/TopicListItemView.vue'

import { useAddingTopicForm } from './hooks'

const viewData = ref<DashTopicsGet200Response>()
const currentPage = ref(1)
const viewLoading = ref(true)
function updateViewData() {
  viewLoading.value = true
  api
    .dashTopicsGet(currentPage.value, 15)
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      viewLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const addingTopicForm = useAddingTopicForm()

function handleCreation() {
  addingTopicForm.open({
    onSuccess() {
      if (currentPage.value === 1) {
        updateViewData()
      } else {
        currentPage.value = 1
      }
    },
  })
}
</script>

<template>
  <div class="card">
    <ViewLayout>
      <template #header>
        <div class="flex justify-end">
          <Button icon="pi pi-plus" label="Create Task" variant="outlined" @click="handleCreation"></Button>
        </div>
      </template>
      <DataView
        lazy
        :loading="viewLoading"
        paginator
        :rows="viewData?.page_size"
        :total-records="viewData?.total"
        :value="viewData?.results"
        @page="
          ({ page }) => {
            currentPage = page + 1
          }
        "
      >
        <template #list="slotProps: { items: DashTopicsGet200ResponseResultsInner[] }">
          <div class="flex flex-col *:mb-3">
            <div v-for="item in slotProps.items" :key="item.id">
              <TopicListItemView :topic-comment="item.comment!" :topic-id="item.id!" @delete-success="updateViewData" />
            </div>
          </div>
        </template>
        <template #paginatorend>
          <div class="flex">
            <Button class="ml-auto" icon="pi pi-refresh" rounded severity="secondary" variant="text" @click="updateViewData"></Button>
          </div>
        </template>
      </DataView>
    </ViewLayout>
  </div>
</template>
