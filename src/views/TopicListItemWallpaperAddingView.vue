<script lang="ts" setup>
import { computed } from 'vue'

import TopicListItemWallpaperAddingImageView from '@/views/TopicListItemWallpaperAddingImageView.vue'

import WallpaperSearchView from './WallpaperSearchView.vue'

const props = defineProps<{
  topicId: number
  excludeWallpaperIds?: number[]
}>()

const emit = defineEmits<{ addingSuccess: [] }>()

const excludeWallpaperIdsSet = computed(() => {
  return new Set(props.excludeWallpaperIds)
})
</script>

<template>
  <WallpaperSearchView>
    <template #item="slotProps">
      <TopicListItemWallpaperAddingImageView
        :disabled="excludeWallpaperIdsSet.has(slotProps.id)"
        :topic-id="props.topicId"
        :wallpaper-id="slotProps.id"
        :wallpaper-url="slotProps.src"
        @adding-success="emit('addingSuccess')"
      />
    </template>
  </WallpaperSearchView>
</template>
