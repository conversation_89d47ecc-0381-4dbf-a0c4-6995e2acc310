<script lang="tsx" setup>
import type { AxiosError } from 'axios'
import { useDialog, Skeleton, Tag, SplitButton, useToast } from 'primevue'
import { onBeforeMount, ref } from 'vue'
import { useRouter } from 'vue-router'

import { api } from '@/api'
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient } from '@/api-client'
import { useConfirm } from '@/components/primevue'
import { RouteName } from '@/router'
import ClientTopicFormView from '@/views/ClientTopicFormView.vue'
import TopicFormView from '@/views/TopicFormView.vue'
import TopicWallpapersView from '@/views/TopicWallpapersView.vue'
const dialog = useDialog()
const confirm = useConfirm()
const toast = useToast()
const router = useRouter()

interface Props {
  topicId: number
  topicComment: string
}

const props = defineProps<Props>()
const internalTopicComment = ref(props.topicComment)

const emit = defineEmits<{
  /**
   * 删除完成事件
   */
  deleteSuccess: []
}>()

const publishedClientsLoading = ref(true)
const publishedClients = ref<Array<DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient>>()
function refreshPublishedClients() {
  publishedClientsLoading.value = true
  api
    .dashTopicsTopicIdClienttopicsGet(props.topicId, ['results.client.name', 'results.client.id'])
    .then((res) => {
      publishedClients.value = res.data.results!.map((i) => i.client!)
    })
    .finally(() => {
      publishedClientsLoading.value = false
    })
}
onBeforeMount(() => {
  refreshPublishedClients()
})

function handleEditing() {
  const dialogInstance = dialog.open(
    <TopicFormView
      initialValues={{ comment: internalTopicComment.value }}
      onSubmit={(values) => {
        api.dashTopicsTopicIdPut(props.topicId, values).then((res) => {
          internalTopicComment.value = res.data.comment
          dialogInstance.close()
        })
      }}
      onCancel={() => {
        dialogInstance.close()
      }}
    />,
    {
      props: {
        header: '编辑专题',
        modal: true,
        style: { minWidth: '30rem' },
      },
    },
  )
}

function handleClientSend() {
  const dialogInstance = dialog.open(
    <ClientTopicFormView
      saveLabel="发 布"
      onSave={(values) => {
        api
          .dashTopicTopicIdPublishClientIdPost(
            props.topicId,
            values.clientId,
            { title: values.title },
            { skipCheckStatusCodes: [409] },
          )
          .then(() => {
            dialogInstance.close()
            refreshPublishedClients()
            toast.add({
              summary: '发布成功',
              life: 3000,
              severity: 'success',
            })
          })
          .catch((err: AxiosError) => {
            if (err.response?.status === 409) {
              toast.add({
                severity: 'error',
                summary: '发布失败',
                detail: '该专题已发布到该客户端',
                life: 3000,
              })
            } else {
              return Promise.reject(err)
            }
          })
      }}
    />,
    {
      props: { modal: true, style: { minWidth: '35rem' }, header: '发布专题' },
    },
  )
}
</script>

<template>
  <div class="border-surface flex items-center rounded-lg border">
    <div class="w-4/5">
      <!-- wallpapers -->
      <div class="overflow-scroll p-2">
        <TopicWallpapersView :topic-id="props.topicId" />
      </div>

      <div class="grid grid-cols-2 px-2 pb-2">
        <div>
          <Skeleton v-if="publishedClientsLoading" height="1.5rem" width="20rem" />
          <template v-else>
            <template v-if="publishedClients?.length">
              <button
                v-for="item in publishedClients"
                :key="item.id"
                class="cursor-pointer"
                @click="
                  () => {
                    router.push({
                      name: RouteName.TopicPublished,
                      query: { 'client-id': item.id },
                    })
                  }
                "
              >
                <Tag class="text-xs!" icon="pi pi-send" severity="success" :value="item.name" />
              </button>
            </template>
            <Tag v-else class="text-xs!" severity="info" value="尚未发布" />
          </template>
        </div>

        <div class="text-surface-500 flex items-center space-x-2 text-sm">
          <Tag class="text-xs!" severity="secondary">备注</Tag>
          <span>{{ internalTopicComment }}</span>
        </div>
      </div>
    </div>

    <div class="ml-auto px-2">
      <SplitButton
        icon="pi pi-cog"
        :model="[
          {
            label: '发布专题',
            icon: 'pi pi-send',
            command: handleClientSend,
          },
          {
            label: '删除专题',
            icon: 'pi pi-trash',
            command() {
              confirm.require({
                message: '确定要删除吗？',
                accept() {
                  api.dashTopicsTopicIdDelete(props.topicId).then(() => {
                    emit('deleteSuccess')
                  })
                },
              })
            },
          },
        ]"
        rounded
        size="small"
        @click="handleEditing"
      ></SplitButton>
    </div>
  </div>
</template>
