<script lang="tsx" setup>
import { useDialog } from 'primevue'
import { Button, Skeleton } from 'primevue'
import { onBeforeMount, ref } from 'vue'

import { api } from '@/api'
import type { DashTopicsTopicIdWallpapersGet200Response } from '@/api-client'

import TopicListItemImageView from './TopicListItemImageView.vue'
import TopicListItemWallpaperAddingView from './TopicListItemWallpaperAddingView.vue'

interface Props {
  topicId: number
}

const props = defineProps<Props>()

const topicWallpaperList = ref<DashTopicsTopicIdWallpapersGet200Response>()
const topicWallpaperListLoading = ref(true)
function refreshTopicWallpaperList() {
  topicWallpaperListLoading.value = true
  api
    .dashTopicsTopicIdWallpapersGet(props.topicId, 1, 1000)
    .then((res) => {
      topicWallpaperList.value = res.data
    })
    .finally(() => {
      topicWallpaperListLoading.value = false
    })
}
onBeforeMount(() => {
  refreshTopicWallpaperList()
})

const dialog = useDialog()

function handleAddingWallpaper() {
  let addedCount = 0
  dialog.open(
    <TopicListItemWallpaperAddingView
      topicId={props.topicId}
      excludeWallpaperIds={topicWallpaperList.value?.results?.map((i) => i.id!)}
      onAddingSuccess={() => {
        addedCount += 1
      }}
    />,
    {
      props: { modal: true, style: { width: '60rem' }, header: '添加壁纸到专题' },
      onClose() {
        if (addedCount) {
          refreshTopicWallpaperList()
        }
      },
    },
  )
}
</script>

<template>
  <ul class="flex gap-4 *:shrink-0">
    <template v-if="topicWallpaperListLoading">
      <Skeleton
        v-for="index in Array(topicWallpaperList?.results?.length || 10)"
        :key="index"
        size="6rem"
      />
    </template>
    <template v-else>
      <li v-if="!topicWallpaperListLoading" class="flex items-center">
        <Button
          class="inline-block size-24!"
          icon="pi pi-plus"
          severity="secondary"
          variant="outlined"
          @click="handleAddingWallpaper"
        ></Button>
      </li>
      <li v-for="wallpaper in topicWallpaperList?.results" :key="wallpaper.id">
        <TopicListItemImageView
          :topic-id="props.topicId"
          :wallpaper-id="wallpaper.id!"
          :wallpaper-url="wallpaper.images!.default!"
          @delete-success="
            () => {
              if (!topicWallpaperList) return
              topicWallpaperList.results = topicWallpaperList.results?.filter(
                (i) => i.id !== wallpaper.id,
              )
            }
          "
        />
      </li>
    </template>
  </ul>
</template>
