<script lang="tsx" setup>
import { But<PERSON>, Select, InputText } from 'primevue'
import { ref, type VNode } from 'vue'

import { api } from '@/api'
import { DashSearchWallpapersV2GetSizedForEnum, type DashSearchWallpapersV2Get200ResponseWallpapersInner } from '@/api-client'
import MyImage from '@/components/MyImage.vue'
import ViewLayout from '@/layout/ViewLayout.vue'
import { sizedForLabelMap } from '@/variables'

defineSlots<{
  item(scoped: { id: number; src: string }): VNode
}>()

const searchText = ref('')
const searchLoading = ref(false)
const filters = ref<{ sizedFor?: DashSearchWallpapersV2GetSizedForEnum }>({})
const sizedForOptions = Object.values(DashSearchWallpapersV2GetSizedForEnum).map((i) => {
  return { label: sizedForLabelMap[i], value: i }
})
const wallpaperList = ref<DashSearchWallpapersV2Get200ResponseWallpapersInner[]>()
function handleSearch() {
  if (searchLoading.value) return
  const text = searchText.value.trim()
  if (!text) return

  searchLoading.value = true
  api
    .dashSearchWallpapersV2Get(text, filters.value.sizedFor)
    .then((res) => {
      wallpaperList.value = res.data.wallpapers
    })
    .finally(() => {
      searchLoading.value = false
    })
}
</script>

<template>
  <div class="card">
    <ViewLayout>
      <template #header>
        <div class="flex gap-2">
          <Select v-model="filters.sizedFor" option-label="label" option-value="value" :options="sizedForOptions" placeholder="适用屏幕" show-clear></Select>
          <InputText v-model.trim="searchText" fluid placeholder="输入任意关键词" @keydown.enter="handleSearch"></InputText>
          <Button class="shrink-0" :disabled="!searchText" icon="pi pi-search" :loading="searchLoading" @click="handleSearch"></Button>
        </div>
      </template>

      <div v-if="wallpaperList?.length">
        <ul class="flex flex-wrap justify-around gap-4">
          <li v-for="item in wallpaperList" :key="item.content_md5">
            <slot :id="item.id" name="item" :src="item.images.default!">
              <MyImage class="overflow-hidden rounded-md" image-class="size-36 object-contain border rounded-md border-surface" preview :src="item.images.default" />
            </slot>
            <div class="text-xs">
              <span class="text-color">score: </span>
              <span class="text-muted-color">
                {{ item.search_score.toFixed(4) }}
              </span>
            </div>
          </li>
        </ul>
      </div>
    </ViewLayout>
  </div>
</template>
