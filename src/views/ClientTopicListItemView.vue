<script lang="ts" setup>
import { Dialog } from 'primevue'
import SplitButton from 'primevue/splitbutton'
import { reactive, ref } from 'vue'

import { api } from '@/api'
import { useConfirm } from '@/components/primevue'
import ClientTopicFormView from '@/views/ClientTopicFormView.vue'
import TopicWallpapersView from '@/views/TopicWallpapersView.vue'

const props = defineProps<{
  id: number
  topicId: number
  title: string
  topicComment: string
  clientName: string
  clientId: string
  publishTime: string
}>()

const internalTitle = ref(props.title)

const emit = defineEmits<{
  /**
   * 移出完成事件
   */
  removeSuccess: []
}>()

const confirm = useConfirm()

const contents = reactive([
  { label: '标题', value: internalTitle },
  { label: '客户端', value: props.clientName },
  { label: '备注', value: props.topicComment },
  { label: '发布时间', value: new Date(props.publishTime).toLocaleString() },
])

const editingDialogVisible = ref(false)

function handleEditPublished(values: { clientId: string; title: string }) {
  api.dashClienttopicsClienttopicIdPut(props.id, { title: values.title }).then((res) => {
    editingDialogVisible.value = false
    internalTitle.value = res.data.title
  })
}

function handleCancel() {
  confirm.require({
    message: '确定要撤销吗？',
    accept: () => {
      api.dashClienttopicsClienttopicIdDelete(props.id).then(() => {
        emit('removeSuccess')
      })
    },
  })
}
</script>

<template>
  <div class="border-surface rounded-lg border px-3 pt-3 pb-1">
    <div class="mb-3 flex items-center justify-between">
      <!-- 信息区域 - 紧凑的内联布局 -->
      <div class="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
        <div v-for="(item, idx) in contents" :key="idx" class="flex items-center">
          <span class="text-muted-color mr-1">{{ item.label }}:</span>
          <span class="text-color font-medium">{{ item.value }}</span>
        </div>
      </div>
      <div>
        <SplitButton
          icon="pi pi-pencil"
          label="编辑"
          :model="[
            {
              label: '撤销发布',
              icon: 'pi pi-times',
              command: handleCancel,
            },
          ]"
          size="small"
          @click="editingDialogVisible = true"
        />
      </div>
    </div>

    <!-- 壁纸展示区域 -->
    <div class="border-surface border-t">
      <div class="overflow-x-scroll p-2">
        <TopicWallpapersView :topic-id="topicId" />
      </div>
    </div>
  </div>

  <Dialog v-model:visible="editingDialogVisible" header="编辑" modal :style="{ width: '30rem' }">
    <ClientTopicFormView
      disabled-client-id
      :initialValues="{
        clientId: clientId,
        title: title,
      }"
      save-icon="pi pi-check"
      save-label="保 存"
      @save="handleEditPublished"
    />
  </Dialog>
</template>
