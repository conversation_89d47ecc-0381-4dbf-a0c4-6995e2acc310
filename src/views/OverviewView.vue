<script lang="ts" setup>
import { Card } from 'primevue'
import { onMounted, ref, watch } from 'vue'

import { api } from '@/api'
import type { DashStatsGet200Response } from '@/api-client'
import ChartDoughnut from '@/components/ChartDoughnut.vue'
import { presetPrimitive, presetName, primaryColor, isDark } from '@/store/palette'

function getPrimaryColorValue() {
  const documentStyle = getComputedStyle(document.documentElement)
  return documentStyle.getPropertyValue(`--p-primary-color`)
}

const primaryColorValue = ref(getPrimaryColorValue())
watch([isDark, presetName, primaryColor], () => {
  primaryColorValue.value = getPrimaryColorValue()
})

const statsData = ref<DashStatsGet200Response>()
onMounted(() => {
  api.dashStatsGet().then((res) => {
    statsData.value = res.data
  })
})
</script>

<template>
  <div class="grid grid-cols-1 gap-4 md:grid-cols-2 2xl:grid-cols-3">
    <Card>
      <template #title>
        <span>图片数量</span>
      </template>
      <template #content>
        <div class="flex h-70 items-center justify-center">
          <ChartDoughnut
            v-if="statsData"
            :data="[
              {
                label: '已加入专题',
                value: statsData.wallpaper_in_topic_count,
                color: primaryColorValue,
              },
              {
                label: '未加入专题',
                value: statsData.wallpaper_count - statsData.wallpaper_in_topic_count,
                color: presetPrimitive?.blue[500],
              },
            ]"
          />
          <template v-else>
            <i class="pi pi-spin pi-spinner"></i>
          </template>
        </div>
      </template>
    </Card>
    <Card>
      <template #title>
        <span>专题数量</span>
      </template>
      <template #content>
        <div class="flex h-70 items-center justify-center">
          <ChartDoughnut
            v-if="statsData"
            :data="[
              { label: '已发布', value: statsData.topic_in_client_count, color: presetPrimitive?.orange[500] },
              { label: '未发布', value: statsData.topic_count - statsData.topic_in_client_count, color: presetPrimitive?.green[500] },
            ]"
          />
          <template v-else>
            <i class="pi pi-spin pi-spinner"></i>
          </template>
        </div>
      </template>
    </Card>
  </div>
</template>
