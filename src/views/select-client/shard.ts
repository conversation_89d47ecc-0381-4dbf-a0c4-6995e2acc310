import { ref } from 'vue'

import { api } from '@/api'
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient } from '@/api-client'

const clients = ref<DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient[]>()
export function useClients() {
  if (clients.value === undefined) {
    api.dashClientsGet().then((resp) => {
      clients.value = resp.data.results
    })
  }
  return clients
}
