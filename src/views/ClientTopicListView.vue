<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { api } from '@/api'
import type { DashTopicsTopicIdClienttopicsGet200Response, DashTopicsTopicIdClienttopicsGet200ResponseResultsInner } from '@/api-client'
import DataView from '@/components/DataView.vue'
import ViewLayout from '@/layout/ViewLayout.vue'
import ClientTopicListItemView from '@/views/ClientTopicListItemView.vue'
import SelectClient from '@/views/select-client/SelectClient.vue'

const router = useRouter()
const route = useRoute()

const filters = reactive<{ clientId?: string }>({
  clientId: route.query['client-id'] as string,
})
const clientTopicList = ref<DashTopicsTopicIdClienttopicsGet200Response>()
const clientTopicListLoading = ref(true)
const currentPage = ref(1)
function refreshClientTopics() {
  clientTopicListLoading.value = true
  api
    .dashClienttopicsGet(currentPage.value, 20, undefined, filters.clientId)
    .then((resp) => {
      clientTopicList.value = resp.data
    })
    .finally(() => {
      clientTopicListLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    refreshClientTopics()
  },
  { immediate: true },
)
watch(filters, () => {
  refreshClientTopics()
  router.replace({
    query: {
      'client-id': filters.clientId,
    },
  })
})
</script>

<template>
  <div class="card">
    <ViewLayout>
      <template #header>
        <SelectClient v-model="filters.clientId" />
      </template>
      <DataView
        lazy
        :loading="clientTopicListLoading"
        paginator
        :rows="clientTopicList?.page_size"
        :total-records="clientTopicList?.total"
        :value="clientTopicList?.results"
        @page="
          ({ page }) => {
            currentPage = page + 1
          }
        "
      >
        <template #list="slotProps: { items: DashTopicsTopicIdClienttopicsGet200ResponseResultsInner[] }">
          <div class="flex flex-col *:mb-3">
            <div v-for="item in slotProps.items" :key="item.id">
              <ClientTopicListItemView
                :id="item.id!"
                :client-id="item.client?.id!"
                :client-name="item.client?.name!"
                :publish-time="item.published_at!"
                :title="item.title!"
                :topic-comment="item.topic?.comment!"
                :topic-id="item.topic?.id!"
                @remove-success="refreshClientTopics"
              />
            </div>
          </div>
        </template>
      </DataView>
    </ViewLayout>
  </div>
</template>
