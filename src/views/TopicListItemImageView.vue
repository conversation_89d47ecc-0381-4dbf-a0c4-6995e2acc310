<script lang="ts" setup>
import { Button } from 'primevue'
import { ref } from 'vue'

import { api } from '@/api'
import MyImage from '@/components/MyImage.vue'
import { useConfirm } from '@/components/primevue'

const confirm = useConfirm()

const props = defineProps<{
  wallpaperId: number
  wallpaperUrl: string
  topicId: number
}>()

const emit = defineEmits<{
  deleteSuccess: []
}>()

const loading = ref(false)

function handleDeleting() {
  confirm.require({
    message: '确定要将壁纸移出该专题吗?',
    accept() {
      loading.value = true
      api
        .dashTopicsTopicIdWallpapersWallpaperIdDelete(props.topicId, props.wallpaperId)
        .then(() => {
          emit('deleteSuccess')
        })
        .finally(() => {
          loading.value = false
        })
    },
  })
}
</script>

<template>
  <div class="group relative">
    <MyImage
      class="block!"
      image-class="border border-surface rounded-md size-24 object-contain block"
      preview
      :pt="{ previewMask: { class: 'rounded-md overflow-hidden' } }"
      :src="wallpaperUrl"
    />
    <div class="absolute top-0 right-0 group-hover:block" :class="loading ? 'block' : 'hidden'">
      <Button
        class="size-6!"
        icon="pi pi-times"
        :loading="loading"
        :pt="{ loadingIcon: { class: 'shrink-0' } }"
        severity="danger"
        size="small"
        @click="handleDeleting"
      ></Button>
    </div>
  </div>
</template>
