<script lang="ts" setup>
import type { FormSubmitEvent } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { InputText, Button, IftaLabel } from 'primevue'
import { ref } from 'vue'
import { z } from 'zod'

import { Form, FormField } from '@/components/primevue'

const schema = z.object({
  comment: z.string().min(1, { message: '请输入备注' }),
})
type Schema = z.infer<typeof schema>

interface Props {
  initialValues?: Schema
}
withDefaults(defineProps<Props>(), { initialValues: () => ({ comment: '' }) })
const emit = defineEmits<{
  submit: [values: Values]
  cancel: []
}>()

interface Values {
  comment: string
}

const resolver = ref(zodResolver(schema))

function handleSubmit(event: FormSubmitEvent) {
  if (!event.valid) return
  emit('submit', event.values as Values)
}
</script>

<template>
  <Form :initialValues :resolver @submit="handleSubmit">
    <FormField name="comment">
      <IftaLabel>
        <InputText id="comment" fluid />
        <label for="comment">备注</label>
      </IftaLabel>
    </FormField>
    <div class="flex justify-end gap-2">
      <Button
        label="取 消"
        severity="secondary"
        type="button"
        variant="text"
        @click="emit('cancel')"
      ></Button>
      <Button label="保 存" type="submit" />
    </div>
  </Form>
</template>
