<script lang="ts" setup>
import { onBeforeMount, ref } from 'vue'

import { api } from '@/api'
import type { DashTopicsTopicIdWallpapersGet200ResponseResultsInner } from '@/api-client'
import MyImage from '@/components/MyImage.vue'

interface Props {
  topicId: number
}
const props = defineProps<Props>()
const wallpapers = ref<DashTopicsTopicIdWallpapersGet200ResponseResultsInner[]>()
onBeforeMount(() => {
  api.dashTopicsTopicIdWallpapersGet(props.topicId, 1, 10).then((res) => {
    wallpapers.value = res.data.results
  })
})
</script>

<template>
  <ul class="flex flex-wrap gap-4">
    <li v-for="wallpaper in wallpapers" :key="wallpaper.id">
      <MyImage alt="" image-class="size-28 object-contain" :src="wallpaper.images?.default" />
    </li>
  </ul>
</template>
