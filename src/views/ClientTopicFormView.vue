<script setup lang="ts">
import type { FormSubmitEvent } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { Button, InputText } from 'primevue'
import Select from 'primevue/select'
import { onMounted, ref } from 'vue'
import { z } from 'zod'

import { api } from '@/api'
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient } from '@/api-client'
import { Form, FormField } from '@/components/primevue'

const emit = defineEmits({
  save(values: z.infer<typeof schema>) {
    return values
  },
})

const schema = z.object({
  clientId: z.preprocess((x) => x ?? '', z.string().nonempty('请选择客户端')),
  title: z.preprocess(
    (x) => x ?? '',
    z.string().min(1, '请输入标题').max(100, '标题长度不能超过 100 个字符'),
  ),
})
type Schema = z.infer<typeof schema>
const resolver = zodResolver(schema)
const props = defineProps<{
  initialValues?: Schema
  saveLabel: string
  saveIcon?: string
  disabledClientId?: boolean
}>()

const clientOptions = ref<DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient[]>()
onMounted(() => {
  api.dashClientsGet().then((resp) => {
    clientOptions.value = resp.data.results
  })
})

function handleSubmit({ values, valid }: FormSubmitEvent) {
  if (!valid) return
  emit('save', values as Schema)
}
</script>

<template>
  <Form :initialValues="props.initialValues" :resolver="resolver" @submit="handleSubmit">
    <FormField name="clientId">
      <label>客户端</label>
      <Select
        :disabled="props.disabledClientId"
        optionLabel="name"
        :options="clientOptions"
        optionValue="id"
        placeholder="请选择客户端"
      >
        <template #option="slotProps">
          <div>{{ slotProps.option.name }}</div>
        </template>
      </Select>
    </FormField>
    <FormField name="title">
      <label>标题</label>
      <InputText placeholder="请输入标题"></InputText>
    </FormField>
    <Button fluid :icon="props.saveIcon" :label="props.saveLabel" type="submit"></Button>
  </Form>
</template>
