<script setup lang="tsx">
import { Column, Tag, Dialog, DataTable, Button, Select } from 'primevue'
import { useDialog } from 'primevue/usedialog'
import { reactive, ref, watch } from 'vue'

import { api } from '@/api'
import type { DashTopicsPost200Response, DashWallpapersGet200Response, DashWallpapersGet200ResponseWallpapersInner } from '@/api-client'
import { DashWallpapersGetSizedForEnum } from '@/api-client'
import MyImage from '@/components/MyImage.vue'
import PagingTable from '@/components/PagingTable.vue'
import ViewLayout from '@/layout/ViewLayout.vue'
import { RouteName } from '@/router'
import { sizedForLabelMap } from '@/variables'

import WallpaperJoinTopicsView from './WallpaperJoinTopicsView.vue'

const dialog = useDialog()

const tableLoading = ref(true)
const viewData = ref<DashWallpapersGet200Response>()

const filters = reactive<{ sizedFor?: DashWallpapersGetSizedForEnum }>({
  sizedFor: undefined,
})
watch(filters, () => {
  updateViewData()
})

const currentPage = ref(1)
function updateViewData() {
  tableLoading.value = true
  return api
    .dashWallpapersGet(currentPage.value, 20, ['wallpapers.id', 'wallpapers.images', 'wallpapers.related_topic_count', 'wallpapers.tags.name', 'current_page', 'current_page_size', 'total'], filters.sizedFor)
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const dialogVisiable = ref(false)
const dialogTableValue = ref<DashTopicsPost200Response[]>()
const dialogTableLoading = ref(true)
function handleJoinInTopic(data: DashWallpapersGet200ResponseWallpapersInner) {
  const vnode = <WallpaperJoinTopicsView wallpaper={data} onUpdate={() => updateViewData()} />
  dialog.open(vnode, {
    props: { header: '添加到专题', modal: true },
  })
}

const sizedForOptions = Object.values(DashWallpapersGetSizedForEnum).map((i) => {
  return { label: sizedForLabelMap[i], value: i }
})
</script>

<template>
  <div class="card">
    <ViewLayout>
      <template #header>
        <div class="flex gap-2">
          <Select v-model="filters.sizedFor" option-label="label" option-value="value" :options="sizedForOptions" placeholder="适用屏幕" show-clear></Select>
        </div>
      </template>
      <PagingTable v-model:current-page="currentPage" data-key="id" :loading="tableLoading" :rows="viewData?.current_page_size" scrollable scrollHeight="flex" :total-records="viewData?.total" :value="viewData?.wallpapers">
        <Column header="图片">
          <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
            <MyImage image-class="size-25 object-contain" preview :src="slotProps.data.images!.default">
              <template #original="imageSlotProps">
                <img alt="preview" :src="slotProps.data.images?.default" :style="imageSlotProps.style" @click="imageSlotProps.previewCallback" />
                <div v-if="slotProps.data.tags?.length" class="mt-4 flex gap-2" @click="(e) => e.stopPropagation()">
                  <Tag v-for="tag in slotProps.data.tags" :key="tag.name">{{ tag.name }}</Tag>
                </div>
              </template>
            </MyImage>
          </template>
        </Column>
        <Column header="已关联专题">
          <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
            <template v-if="slotProps.data.related_topic_count === 0">
              <Tag severity="secondary">0</Tag>
            </template>
            <button
              v-else
              class="cursor-pointer"
              @click="
            () => {
              if (slotProps.data.related_topic_count === 0) return

              dialogVisiable = true
              dialogTableLoading = true
              api
                .dashWallpapersWallpaperIdTopicsGet(slotProps.data.id!)
                .then((resp) => {
                  dialogTableValue = resp.data.topics
                })
                .finally(() => {
                  dialogTableLoading = false
                })
            }
          "
            >
              <Tag>{{ slotProps.data.related_topic_count }}</Tag>
            </button>
          </template>
        </Column>
        <Column>
          <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
            <Button icon="pi pi-plus" rounded size="small" @click="handleJoinInTopic(slotProps.data)" />
          </template>
        </Column>
      </PagingTable>
    </ViewLayout>
  </div>

  <Dialog
    v-model:visible="dialogVisiable"
    class="min-w-200"
    header="关联专题"
    @after-hide="
      () => {
        dialogTableValue = undefined
      }
    "
  >
    <DataTable :loading="dialogTableLoading" :value="dialogTableValue">
      <Column field="comment" header="专题备注"></Column>
      <Column>
        <template #body="slotProps: { data: DashTopicsPost200Response }">
          <Button v-slot="slot" asChild variant="link">
            <RouterLink :class="slot.class" :to="{ name: RouteName.TopicWallpapers, params: { id: slotProps.data.id } }"> 前往专题 </RouterLink>
          </Button>
        </template>
      </Column>
    </DataTable>
  </Dialog>
</template>
