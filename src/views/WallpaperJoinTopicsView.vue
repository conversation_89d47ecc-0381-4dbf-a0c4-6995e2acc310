<script setup lang="tsx">
import { <PERSON>umn, Button, useToast, Tag } from 'primevue'
import { ref, watch } from 'vue'

import { api } from '@/api'
import type {
  DashTopicsGet200Response,
  DashTopicsGet200ResponseResultsInner,
  DashWallpapersGet200ResponseWallpapersInner,
} from '@/api-client'
import MyImage from '@/components/MyImage.vue'
import PagingTable from '@/components/PagingTable.vue'
import { useConfirm } from '@/components/primevue'

import { useAddingTopicForm } from './hooks'

const confirm = useConfirm()
const toast = useToast()

interface Props {
  wallpaper: DashWallpapersGet200ResponseWallpapersInner
}
const props = defineProps<Props>()
const emit = defineEmits<{
  /**
   * 壁纸添加成功时事件
   */
  update: []
}>()

const viewData = ref<DashTopicsGet200Response>()
const currentPage = ref(1)
const tableLoading = ref(true)
function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicsGet(currentPage.value, 5, props.wallpaper.id, [
      'results.id',
      'results.comment',
      'total',
      'page',
      'page_size',
    ])
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const addingTopicForm = useAddingTopicForm()
function handleAddingTopic() {
  addingTopicForm.open({
    onSuccess() {
      if (currentPage.value === 1) {
        updateViewData()
      } else {
        currentPage.value = 1
      }
    },
  })
}

function handleWallpaperJoinInTopic(topic: DashTopicsGet200ResponseResultsInner) {
  confirm.require({
    message: `是否添加到专题 [${topic.comment}]`,
    accept: () => {
      api.dashTopicsTopicIdWallpapersWallpaperIdPut(topic.id!, props.wallpaper.id!).then(() => {
        updateViewData()
        toast.add({
          summary: '壁纸已成功添加到专题',
          life: 3000,
          severity: 'success',
        })
        emit('update')
      })
    },
  })
}
</script>
<template>
  <div class="flex gap-8">
    <div class="self-center">
      <MyImage
        imageClass="size-48 object-contain"
        preview
        :src="props.wallpaper.images!.default"
      ></MyImage>
      <div class="mt-4 flex gap-2">
        <Tag v-for="tag in props.wallpaper.tags" :key="tag.name" :value="tag.name"></Tag>
      </div>
    </div>

    <div class="flex flex-col">
      <div class="ml-auto">
        <Button icon="pi pi-plus" label="新增专题" size="small" @click="handleAddingTopic" />
      </div>
      <PagingTable
        v-model:current-page="currentPage"
        empty-text="没有可添加的专题"
        :loading="tableLoading"
        :rows="viewData?.page_size"
        :total-records="viewData?.total"
        :value="viewData?.results"
      >
        <Column field="comment" header="专题备注"></Column>
        <Column field="wallpaper_count" header="壁纸"></Column>
        <Column field="published_count" header="已发布"></Column>
        <Column>
          <template #body="slotProps: { data: DashTopicsGet200ResponseResultsInner }">
            <Button
              icon="pi pi-plus"
              rounded
              size="small"
              @click="handleWallpaperJoinInTopic(slotProps.data)"
            ></Button>
          </template>
        </Column>
      </PagingTable>
    </div>
  </div>
</template>
