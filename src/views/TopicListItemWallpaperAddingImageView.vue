<script lang="ts" setup>
import { Button, useToast } from 'primevue'
import { computed, ref } from 'vue'

import { api } from '@/api'
import MyImage from '@/components/MyImage.vue'

const props = defineProps<{
  wallpaperId: number
  wallpaperUrl: string
  topicId: number
  disabled?: boolean
}>()

const emit = defineEmits<{
  addingSuccess: []
}>()

const toast = useToast()
const loading = ref(false)
const bingo = ref(false)
function onClichButton() {
  loading.value = true
  setTimeout(() => {
    api
      .dashTopicsTopicIdWallpapersWallpaperIdPut(props.topicId, props.wallpaperId)
      .then(() => {
        loading.value = false
        bingo.value = true
        emit('addingSuccess')
        toast.add({
          summary: '壁纸已成功添加到专题',
          life: 3000,
          severity: 'success',
        })
      })
      .finally(() => {
        loading.value = false
      })
  }, 1500)
}

const checked = computed(() => {
  return props.disabled || bingo.value
})
</script>

<template>
  <div class="group relative">
    <MyImage
      class="overflow-hidden rounded-md"
      image-class="size-36 object-contain border rounded-md border-surface"
      preview
      :src="wallpaperUrl"
    ></MyImage>
    <div
      class="absolute top-0 right-0"
      :class="['group-hover:block', loading || checked ? 'block' : 'hidden']"
    >
      <Button
        class="size-6!"
        :disabled="checked"
        :icon="`pi ${checked ? 'pi-check' : 'pi-plus'}`"
        :loading="loading"
        :pt="{ loadingIcon: { class: 'shrink-0' } }"
        size="small"
        @click="onClichButton"
      ></Button>
    </div>
  </div>
</template>
