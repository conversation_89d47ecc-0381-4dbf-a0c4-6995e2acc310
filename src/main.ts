import './styles.css'
import 'primeicons/primeicons.css'

import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import DialogService from 'primevue/dialogservice'
import ToastService from 'primevue/toastservice'
import { createApp } from 'vue'

import { getPreset } from '@/store/palette'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(router)

app.use(PrimeVue, {
  theme: {
    preset: await getPreset(),
    options: {
      darkModeSelector: '.dark',
      cssLayer: {
        name: 'primevue',
        order: 'properties, theme, base, primevue, components, utilities',
      },
    },
  },
})

app.use(ToastService)
app.use(ConfirmationService)
app.use(DialogService)

app.mount('#app')
