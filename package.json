{"name": "walldash", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "openapi-generator": "openapi-generator-cli generate -g typescript-axios -o ./src/api-client -i", "prepare": "husky"}, "dependencies": {"@primeuix/themes": "^1.2.1", "@primevue/forms": "^4.3.6", "@tailwindcss/vite": "^4.1.11", "async-mutex": "^0.5.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "primeicons": "^7.0.0", "primevue": "^4.3.6", "tailwindcss": "^4.1.11", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "zod": "^4.0.10"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@types/tinycolor2": "^1.4.6", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-vue": "~10.3.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "sass-embedded": "^1.92.1", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}